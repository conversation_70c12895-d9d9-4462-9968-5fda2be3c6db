package com.pugwoo.admin.web.tasklog;

import com.pugwoo.admin.entity.AdminTaskLogDO;
import com.pugwoo.admin.utils.ClassUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Aspect
@Component
@Slf4j
public class TaskLogAOP {

    /**  采用线程池，主线程执行业务代码，遇到监控的逻辑新开线程去执行，保证事务不共用 */
    private final ExecutorService pool = Executors.newFixedThreadPool(1);

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @Around("@annotation(com.pugwoo.admin.web.tasklog.TaskLog) || @annotation(org.springframework.scheduling.annotation.Scheduled)")
    public Object aroundMethod(ProceedingJoinPoint joinpoint) throws Throwable {
        //  1. 获取注解内的TaskName 获取切点的方法签名
        MethodSignature signature = (MethodSignature) joinpoint.getSignature();
        //  获取方法名称
        Method method = signature.getMethod();
        Object[] args = joinpoint.getArgs();

        TaskLog taskLog = method.getAnnotation(TaskLog.class);
        Scheduled scheduled = method.getAnnotation(Scheduled.class);

        long start = System.currentTimeMillis();
        //  2. 前置处理：插入TaskLogDO对象
        Future<AdminTaskLogDO> taskLogDO = pool.submit(() -> generateNewOne(method, args, taskLog, scheduled));

        //  3. 调用接口方法(如果有异常，在下面的try-catch块中捕获、记录后再抛出)
        Object result;
        try {
            //  真正调用接口方法
            result = joinpoint.proceed();
            //  4. 成功调用时记录执行时长，回写DO
            pool.submit(() -> updateSuccessfulOne(taskLogDO, start));
            //  5. 返回调用结果，监控只做记录
            return result;
        } catch (Throwable e) {
            //  如果catch到了异常，执行失败并获取对应的报错栈信息，回写Log对象
            StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer, true));
            //  更新异常记录对象
            pool.submit(() -> updateFailedOne(taskLogDO, writer.toString(), start));
            //  继续抛出异常，监控只做记录
            throw e;
        }
    }

    private AdminTaskLogDO generateNewOne(Method method, Object[] args,
                                          TaskLog taskLog, Scheduled scheduled){
        AdminTaskLogDO adminTaskLogDO = new AdminTaskLogDO();
        adminTaskLogDO.setTaskName(taskLog == null ? "" : taskLog.taskName());
        adminTaskLogDO.setClassName(method.getDeclaringClass().getName());
        adminTaskLogDO.setMethodName(ClassUtils.getMethodSignature(method));

        if (taskLog != null && StringTools.isNotBlank(taskLog.taskCode())) {
            adminTaskLogDO.setTaskCode(taskLog.taskCode());
        } else {
            adminTaskLogDO.setTaskCode(adminTaskLogDO.getClassName() + "." + adminTaskLogDO.getMethodName());
        }

        if (scheduled != null) {
            adminTaskLogDO.setCronExpression(scheduled.cron());
            if (scheduled.fixedRate() != 0) {
                adminTaskLogDO.setFixRateMs((int) scheduled.fixedRate());
            } else if (scheduled.fixedDelay() != 0) {
                adminTaskLogDO.setFixRateMs((int) scheduled.fixedDelay());
            }
        }

        try {
            adminTaskLogDO.setRunIp(String.join(";", NetUtils.getIpv4IPs()));
        } catch (Exception e) {
            adminTaskLogDO.setRunIp("EXCEPTION " + e.getClass().getName());
            log.error("fail to get machine ips", e);
        }
        Integer timeout = taskLog == null ? -1 : taskLog.timeout();
        adminTaskLogDO.setTimeoutSecond(Objects.equals(timeout, -1) ? null : timeout);
        adminTaskLogDO.setArgs(JSON.toJson(args));
        adminTaskLogDO.setStatus("NEW");
        adminTaskLogDO.setCostMs(0);
        dbHelper.insertWithNull(adminTaskLogDO);
        return adminTaskLogDO;
    }

    private void updateFailedOne(Future<AdminTaskLogDO> future, String errorMsg, long start) {
        long end = System.currentTimeMillis();
        AdminTaskLogDO one = null;
        try {
            one = future.get();
            one.setCostMs((int)(end - start));
            one.setStatus("FAIL");
            one.setErrorMsg(errorMsg);
            dbHelper.update(one);
        } catch (Exception e) {
            log.error("【定时任务执行失败】更新失败:{}", JSON.toJson(one), e);
        }
    }

    private void updateSuccessfulOne(Future<AdminTaskLogDO> future, long start) {
        long end = System.currentTimeMillis();
        AdminTaskLogDO one = null;
        try {
            one = future.get();
            one.setCostMs((int)(end - start));
            one.setStatus("SUCCESS");
            dbHelper.update(one);
        } catch (Exception e) {
            log.error("【定时任务执行成功】更新失败:{}", JSON.toJson(one), e);
        }
    }

}
