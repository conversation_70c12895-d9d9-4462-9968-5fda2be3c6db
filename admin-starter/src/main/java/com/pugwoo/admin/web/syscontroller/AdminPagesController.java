package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.utils.NoPermission;
import com.pugwoo.admin.utils.NotRequireLogin;
import com.pugwoo.admin.utils.Permission;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 所有纯页面的链接
 */
@Controller
public class AdminPagesController {

    /**iframe首页*/
    @NoPermission @RequestMapping("/")
    public String index() {
        return "index";
    }

    @NotRequireLogin
    @GetMapping("/admin_login/login")
    public String login() {
        return "login";
    }

    @NotRequireLogin
    @GetMapping("/admin_login/no_permission")
    public String noPermission() {
        return "no_permission";
    }

    @Permission(value = "AdminUserRead", name = "用户列表")
    @GetMapping("/admin_user/list")
    public String listUser() {
        return "admin_system/user_list";
    }

    /**个人页面*/
    @NoPermission @GetMapping("/admin_user/my")
    public String my() {
        return "admin_system/my";
    }

    @Permission(value = "AdminUserDeptRead", name = "用户部门")
    @GetMapping("/admin_user/department/list")
    public String departmentList(){
        return "admin_system/department_list";
    }

    @Permission(value = "AdminRoleUrlRead", name = "系统角色")
    @GetMapping("/admin_role/list")
    public String list(Model model) {
        return "admin_system/role_list";
    }

    @Permission(value = "AdminRoleUrlRead", name = "系统权限")
    @GetMapping("/admin_url/list")
    public String listUrl(Model model) {
        return "admin_system/url_list";
    }

    @Permission(value = "AdminDictRead", name = "数据字典")
    @GetMapping("/admin_dict/list")
    public String list() {
        return "admin_system/dict_list";
    }

    /**异常页面*/
    @Permission(value = "AdminLogRead", name = "系统异常列表")
    @GetMapping("/admin_log/exception/list")
    public String exceptionList(Model model){
        return "admin_system/log_exception";
    }

    /**慢web页面*/
    @Permission(value = "AdminLogRead", name = "系统慢url列表")
    @GetMapping("/admin_log/slowweb/list")
    public String slowWebList(Model model){
        return "admin_system/log_slow_web";
    }

    /**慢web页面*/
    @Permission(value = "AdminLogRead", name = "系统慢sql列表")
    @GetMapping("/admin_log/slowsql/list")
    public String slowSqlList(Model model){
        return "admin_system/log_slow_sql";
    }

}
