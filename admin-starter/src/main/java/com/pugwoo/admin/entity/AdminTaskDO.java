package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("t_admin_task")
public class AdminTaskDO extends AdminCoreDO  {

    /** 任务名称<br/>Column: [task_name] */
    @Column(value = "task_name")
    private String taskName;

    /** 任务编号，任务的唯一标识，可以指定，没有指定时值为class_name.method_name<br/>Column: [task_code] */
    @Column(value = "task_code")
    private String taskCode;

    /** 执行的类的名称<br/>Column: [class_name] */
    @Column(value = "class_name")
    private String className;

    /** 执行的方法名称<br/>Column: [method_name] */
    @Column(value = "method_name")
    private String methodName;

    /** 如果是定时任务有cron表达式，则记录这里<br/>Column: [cron_expression] */
    @Column(value = "cron_expression")
    private String cronExpression;

    /** 如果任务有固定的频率，fixed和delay都记录在这里<br/>Column: [fix_rate_ms] */
    @Column(value = "fix_rate_ms")
    private Integer fixRateMs;

}