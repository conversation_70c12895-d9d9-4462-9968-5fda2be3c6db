spring:
  datasource:
    url: ***************************************************************************************************************
    username: dev
    password: devdev
    driver-class-name: com.mysql.cj.jdbc.Driver
    validationQuery: SELECT 1
    testWhileIdle: true
    testOnBorrow: true
    testOnReturn: false
  data:
    redis:
      host: *************
      port: 6379
      password: devdev
      database: 2

