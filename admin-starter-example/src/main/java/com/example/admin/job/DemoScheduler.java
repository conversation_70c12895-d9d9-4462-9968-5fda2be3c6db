package com.example.admin.job;

import com.pugwoo.wooutils.log.MDCUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 特别说明：2024年4月22日22:38:25
 * 由于alibaba的ttl 和一个mdc adapter在spring boot3下导致mdc失效，所以这里就手工来解决线程池传递mdc的问题了，也能用的，而且还能减少alibaba ttl的依赖
 */
@Slf4j
@Component
public class DemoScheduler {

    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    private final ExecutorService executorService1 = Executors.newVirtualThreadPerTaskExecutor();

    @Scheduled(cron = "0 0/1 * * * ?")
    public void helloScheduler() {
        log.info("this is a scheduler, run every 10 minute");

        final String key = "requestUuid";
        log.info("异步前的 traceId: {}", MDC.get(key));

        // 说明：CompletableFuture这种不支持，同时也不建议用CompletableFuture，因为它是整个jvm全局的，影响不可控
        // 如果实在要用，就要手工套一层MDCUtils.withMdc
        CompletableFuture.runAsync(MDCUtils.withMdc(() -> log.info("future后的 traceId: {}", MDC.get(key))));

        executorService.submit(MDCUtils.withMdc(() -> {
            log.info("线程池后的 traceId: {}", MDC.get(key));
        }));

        new Thread(MDCUtils.withMdc(() -> {log.info("新线程后的 traceId: {}", MDC.get(key));})).start();

        Thread.ofVirtual().start(MDCUtils.withMdc(() -> {log.info("新协程后的 traceId: {}", MDC.get(key));}));

        executorService1.submit(MDCUtils.withMdc(() -> {
            log.info("虚拟线程池后的 traceId: {}", MDC.get(key));
        }));
    }

}
